package cn.genn.ai.hub.plugin.impl.memory.service;

import cn.genn.ai.hub.core.api.memory.MemoryEventsSearchResponse;
import cn.genn.ai.hub.core.plugin.PluginContext;
import cn.genn.ai.hub.core.plugin.PluginRequest;
import cn.genn.ai.hub.plugin.common.AbstractGennAIPlugin;
import cn.genn.ai.hub.plugin.common.PluginEnum;
import cn.genn.ai.hub.plugin.impl.memory.client.MemoryEventsSearchAbilityClient;
import cn.genn.ai.hub.plugin.impl.memory.model.MemoryEventsSearchInputParam;
import cn.genn.ai.hub.plugin.impl.memory.model.MemoryEventsSearchOutputParam;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 情景记忆事件检索插件
 *
 * <AUTHOR>
 */
@Component("memoryEventSearch")
@Slf4j
public class MemoryEventSearchPlugin extends AbstractGennAIPlugin<MemoryEventsSearchInputParam, MemoryEventsSearchOutputParam> {

    @Resource
    private MemoryEventsSearchAbilityClient memoryEventsSearchAbilityClient;

    @Override
    public String getPluginKey() {
        return PluginEnum.MEMORY_EVENT_SEARCH.getPluginKey();
    }

    @Override
    protected MemoryEventsSearchOutputParam innerExecute(PluginRequest request, PluginContext pluginContext, MemoryEventsSearchInputParam input) {
        try {
            // 调用情景记忆事件检索
            MemoryEventsSearchResponse response = memoryEventsSearchAbilityClient.search(
                request.getWorkflowId(),
                input.getQuery(),
                input.getTopK(),
                input.getVectorModel()
            );

            // 转换为输出参数
            List<MemoryEventsSearchOutputParam.MemoryEventSearchItem> items = response.getItems().stream()
                .map(item -> MemoryEventsSearchOutputParam.MemoryEventSearchItem.builder()
                    .id(item.getId())
                    .score(item.getScore())
                    .subject(item.getSubject())
                    .time(item.getTime())
                    .event(item.getEvent())
                    .cause(item.getCause())
                    .process(item.getProcess())
                    .build())
                .collect(Collectors.toList());

            return MemoryEventsSearchOutputParam.builder()
                .items(items)
                .resultCount(response.getResultCount())
                .originalQuery(response.getOriginalQuery())
                .build();

        } catch (Exception e) {
            log.error("Error in MemoryEventsSearchPlugin execution", e);
            throw new RuntimeException("Failed to execute memory events search plugin", e);
        }
    }

    @Override
    public PluginCheckResult checkValid(PluginContext context) {
        Map<String, Object> params = context.getRequest().getParams();
        MemoryEventsSearchInputParam inputParam = JsonUtils.mapToObject(params, MemoryEventsSearchInputParam.class);
        context.setInputParam(inputParam);

        if (StrUtil.isBlank(inputParam.getQuery())) {
            return new PluginCheckResult(false, "必填参数 'query' 缺失");
        }

        if (inputParam.getTopK() != null && inputParam.getTopK() <= 0) {
            return new PluginCheckResult(false, "参数 'topK' 必须大于0");
        }

        return new PluginCheckResult(true, null);
    }
}
