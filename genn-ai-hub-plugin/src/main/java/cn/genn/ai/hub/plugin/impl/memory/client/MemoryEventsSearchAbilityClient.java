package cn.genn.ai.hub.plugin.impl.memory.client;

import cn.genn.ai.hub.core.api.memory.MemoryEventsSearchAbilityApi;
import cn.genn.ai.hub.core.api.memory.MemoryEventsSearchRequest;
import cn.genn.ai.hub.core.api.memory.MemoryEventsSearchResponse;
import cn.genn.ai.hub.plugin.common.aop.LogMonitor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 情景记忆事件检索能力客户端
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class MemoryEventsSearchAbilityClient {

    private final MemoryEventsSearchAbilityApi memoryEventsSearchAbilityApi;

    @LogMonitor(key = "memoryEventsSearch", description = "情景记忆事件-检索")
    public MemoryEventsSearchResponse search(String workflowId, String query, Integer topK, String vectorModel) {
        MemoryEventsSearchRequest request = MemoryEventsSearchRequest.builder()
            .workflowId(workflowId)
            .query(query)
            .topK(topK)
            .vectorModel(vectorModel)
            .build();
        return memoryEventsSearchAbilityApi.search(request);
    }
}
