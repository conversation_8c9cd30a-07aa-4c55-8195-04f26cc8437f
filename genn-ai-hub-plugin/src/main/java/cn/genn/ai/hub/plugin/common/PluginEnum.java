package cn.genn.ai.hub.plugin.common;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum PluginEnum {

    AI_CHAT("aiChat"),
    KB_SEARCH("kbSearch"),
    KB_CREATE_CASES("kbCreateCasesPlugin"),
    FILE_PARSE("fileParse"),
    MD2File("md2File"),
    FS_WIKI_SHEET("fsWikiSheet"),
    FS_BITABLE_GET_BASE_INFO("fsBitableGetBaseInfo"),
    FS_BITABLE_LIST_TABLES("fsBitableListTables"),
    FS_BITABLE_ADD_RECORDS("fsBitableAddRecords"),
    FS_BITABLE_UPDATE_RECORDS("fsBitableUpdateRecords"),
    FS_BITABLE_SEARCH_RECORDS("fsBitableSearchRecords"),
    FS_GET_USER("fsGetUser"),
    FS_GET_JOB_LEVEL("fsGetJobLevel"),
    FS_IM_MESSAGE_SEND("fsImMessageSend"),
    CACHE_ADD("cacheAdd"),
    CACHE_DELETE("cacheDelete"),
    CACHE_QUERY("cacheQuery"),
    CV_PROCESS("cvProcess"),
    CURRENT_USER("currentUser"),
    MEMORY_EVENT_WRITE("memoryEventWrite"),
    MEMORY_EVENT_SEARCH("memoryEventSearch"),
    ;

    private final String pluginKey;

    PluginEnum(String pluginKey) {
        this.pluginKey = pluginKey;
    }
}
