package cn.genn.ai.hub.core.api.kb;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 知识库检索结果项
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class KbSearchResultItem {

    /**
     * 知识库ID
     */
    private Long repoId;

    /**
     * 集合ID
     */
    private String collectionId;

    /**
     * 数据键
     */
    private String dataKey;

    /**
     * 问答对唯一码
     */
    private String qaPairKey;

    /**
     * 索引键
     */
    private String indexKey;

    /**
     * 问题
     */
    private String question;

    /**
     * 答案
     */
    private String answer;

    /**
     * 案例库ID
     */
    private Long casesId;

    /**
     * 标题
     */
    private String title;

    /**
     * 案例详情
     */
    private String content;

    /**
     * 影响内容
     */
    private String impact;

    /**
     * 扩展数据
     */
    private String extData;

    /**
     * 相关度分数
     */
    private Double score;

    /**
     * 文档来源名称
     */
    private String sourceName;

    /**
     * 文档来源外链
     */
    private String sourceUrl;

}
