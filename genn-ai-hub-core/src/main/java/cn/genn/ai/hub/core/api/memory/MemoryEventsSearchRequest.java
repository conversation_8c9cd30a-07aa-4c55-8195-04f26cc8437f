package cn.genn.ai.hub.core.api.memory;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 情景记忆事件检索请求
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemoryEventsSearchRequest {

    private String workflowId;

    /**
     * 用户查询文本
     */
    private String query;

    /**
     * 最大返回结果数量，默认10
     */
    @Builder.Default
    private Integer topK = 10;

    /**
     * 向量模型，默认bge-m3
     */
    @Builder.Default
    private String vectorModel = "m3e-large";
}
