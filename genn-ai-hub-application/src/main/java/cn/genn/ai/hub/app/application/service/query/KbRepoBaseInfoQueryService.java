package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.KbRepoBaseInfoAssembler;
import cn.genn.ai.hub.app.application.assembler.KbRepoCategoryAssembler;
import cn.genn.ai.hub.app.application.dto.CollaboratorDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoBaseInfoDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoCatDetailDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoCategoryDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoBaseInfoQuery;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCatDetailQuery;
import cn.genn.ai.hub.app.application.enums.RepoTypeEnum;
import cn.genn.ai.hub.app.application.enums.TagTypeEnum;
import cn.genn.ai.hub.app.application.query.TagsQuery;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.infrastructure.config.auth.AuthSqlInject;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoBaseInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepoBaseInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepoCategoryRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoBaseInfoPO;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.utils.GennUtils;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.genn.spring.boot.starter.upm.component.IUpmUserService;
import cn.genn.spring.boot.starter.upm.model.UpmUserDTO;
import cn.genn.spring.boot.starter.upm.model.UpmUserQuery;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoBaseInfoQueryService {

    private final KbRepoBaseInfoMapper mapper;
    private final KbRepoBaseInfoAssembler assembler;
    private final KbRepoCategoryAssembler categoryAssembler;
    private final KbRepoBaseInfoRepositoryImpl baseInfoRepository;
    private final KbRepoCategoryRepositoryImpl categoryRepository;
    private final IUpmUserService userService;
    private final TagsQueryService tagsQueryService;
    private final TeamMemberQueryService teamMemberQueryService;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return KbRepoBaseInfoDTO分页对象
     */
    public PageResultDTO<KbRepoBaseInfoDTO> page(KbRepoBaseInfoQuery query) {
        // 处理标签相关的查询
        Set<Long> kbIds = null;
        // 如果有标签相关的查询条件
        if (CollUtil.isNotEmpty(query.getTagIds()) || CharSequenceUtil.isNotEmpty(query.getTagName())) {
            TagsQuery tagsQuery = TagsQuery
                .builder()
                .tagIds(query.getTagIds())
                .teamId(query.getTeamId())
                .tagType(TagTypeEnum.KNOWLEDGE)
                .name(query.getTagName())
                .build();
            // 使用标签查询服务的方法获取资源key列表
            Set<String> resourceKeys = tagsQueryService.getResourceKeysByTagConditions(tagsQuery);
            if (resourceKeys.isEmpty()) {
                // 如果没有找到任何资源key，返回空结果
                return PageResultDTO.empty();
            }
            kbIds = resourceKeys.stream()
                .map(Long::valueOf)
                .collect(Collectors.toSet());
        }
        LambdaQueryWrapper<KbRepoBaseInfoPO> queryWrapper = new QueryWrapper<KbRepoBaseInfoPO>().lambda();
        queryWrapper
            .eq(Objects.nonNull(query.getRepoType()), KbRepoBaseInfoPO::getRepoType, query.getRepoType())
            .like(StrUtil.isNotBlank(query.getName()), KbRepoBaseInfoPO::getName, query.getName());
        if (CollUtil.isNotEmpty(kbIds)) {
            queryWrapper.in(KbRepoBaseInfoPO::getId, kbIds);
        }
        if (query.getTeamId() == null) {
            queryWrapper.in(KbRepoBaseInfoPO::getCreateUserId, CurrentUserHolder.getUserId());
            queryWrapper.isNull(KbRepoBaseInfoPO::getTeamId);
        }else {
            queryWrapper.eq(KbRepoBaseInfoPO::getTeamId, query.getTeamId());
        }
        queryWrapper.eq(KbRepoBaseInfoPO::getDeleted, DeletedTypeEnum.NOT_DELETED);
        queryWrapper.orderByDesc(KbRepoBaseInfoPO::getUpdateTime);
        PageResultDTO<KbRepoBaseInfoDTO> pageResult = SpringUtil.getBean(KbRepoBaseInfoQueryService.class).queryPage(query, queryWrapper);
        if (GennUtils.pageIsEmpty(pageResult)){
            return pageResult;
        }
        fillTagsInfo(pageResult.getList());
        fillCollaborators(pageResult.getList());
        return pageResult;
    }


    /**
     * 根据id查询
     *
     * @param query
     * @return KbRepoBaseInfoDTO
     */
    public KbRepoBaseInfoDTO get(IdQuery query) {
        KbRepoBaseInfoDTO kbRepoBaseInfoDTO = assembler.PO2DTO(mapper.selectById(query.getId()));
        if (kbRepoBaseInfoDTO == null) {
            return null;
        }
        fillTagsInfo(Collections.singletonList(kbRepoBaseInfoDTO));
        fillCollaborators(Collections.singletonList(kbRepoBaseInfoDTO));
        return kbRepoBaseInfoDTO;
    }

    /**
     * 获取知识库列表的所有目录结构
     *
     * @param query
     * @return
     */
    public List<KbRepoCatDetailDTO> getKbRepoCatDetail(KbRepoCatDetailQuery query) {
        RepoTypeEnum repoType = query.getRepoType();
        if (RepoTypeEnum.isRepo(repoType)) {
            // 再查询当前目录下的具体的知识库
            List<KbRepoBaseInfoDTO> baseInfoDTOS = baseInfoRepository.getKbRepoCatDetail(query);
            // 按创建时间倒序 重新进行数据组合
            return dataComposition(Lists.newArrayList(), baseInfoDTOS);
        }
        // 先查询目录结构
        List<KbRepoCategoryDTO> catDTOS = categoryRepository.getKbRepoCatDetail(query);
        // 再查询当前目录下的具体的知识库
        List<KbRepoBaseInfoDTO> baseInfoDTOS = baseInfoRepository.getKbRepoCatDetail(query);
        // 按创建时间倒序 重新进行数据组合
        return dataComposition(catDTOS, baseInfoDTOS);
    }

    @AuthSqlInject
    public PageResultDTO<KbRepoBaseInfoDTO> queryPage(KbRepoBaseInfoQuery query, LambdaQueryWrapper<KbRepoBaseInfoPO> queryWrapper) {
        return assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), queryWrapper));
    }

    /**
     * 按创建时间倒序 重新进行数据组合
     *
     * @param catDTOS
     * @param baseInfoDTOS
     */
    private List<KbRepoCatDetailDTO> dataComposition(List<KbRepoCategoryDTO> catDTOS, List<KbRepoBaseInfoDTO> baseInfoDTOS) {
        List<KbRepoCatDetailDTO> result = new ArrayList<>();
        // 合并目录和知识库的创建用户ID
        Set<Long> userIds = Stream.concat(
            catDTOS.stream().map(KbRepoCategoryDTO::getCreateUserId),
            baseInfoDTOS.stream().map(KbRepoBaseInfoDTO::getCreateUserId)
        ).collect(Collectors.toSet());
        // 获取用户头像
        Map<Long, String> userAvatar = new HashMap<>();
        if (!userIds.isEmpty()) {
            UpmUserQuery query = new UpmUserQuery();
            query.setUserIdList(new ArrayList<>(userIds));
            userAvatar = userService.conditionList(query).stream()
                .filter(o -> StrUtil.isNotBlank(o.getAvatar()))
                .collect(Collectors.toMap(UpmUserDTO::getId, UpmUserDTO::getAvatar));
        }
        // 处理目录类型数据
        result.addAll(convertToCatDetailDTO(catDTOS, true, userAvatar));
        // 处理知识库类型数据
        result.addAll(convertToCatDetailDTO(baseInfoDTOS, false, userAvatar));
        // 按创建时间倒序排序
        result.sort(Comparator.comparing(KbRepoCatDetailDTO::getCreateTime).reversed());
        return result;
    }

    private List<KbRepoCatDetailDTO> convertToCatDetailDTO(List<?> dtos, boolean isCat, Map<Long, String> userAvatar) {
        List<KbRepoCatDetailDTO> detailDTOS = new ArrayList<>();
        for (Object dto : dtos) {
            KbRepoCatDetailDTO detailDTO;
            if (isCat) {
                KbRepoCategoryDTO catDTO = (KbRepoCategoryDTO) dto;
                detailDTO = categoryAssembler.KbRepoCategoryDTO2KbRepoCatDetailDTO(catDTO);
                detailDTO.setUniqueId("cat_" + catDTO.getId());
            } else {
                KbRepoBaseInfoDTO baseInfoDTO = (KbRepoBaseInfoDTO) dto;
                detailDTO = assembler.KbRepoBaseInfoDTO2KbRepoCatDetailDTO(baseInfoDTO);
                detailDTO.setUniqueId("repo_" + baseInfoDTO.getId());
            }
            detailDTO.setIsCat(isCat);
            detailDTO.setCreateUserAvatar(userAvatar.get(getCreateUserId(dto)));
            detailDTOS.add(detailDTO);
        }
        return detailDTOS;
    }

    private Long getCreateUserId(Object dto) {
        if (dto instanceof KbRepoCategoryDTO) {
            return ((KbRepoCategoryDTO) dto).getCreateUserId();
        } else if (dto instanceof KbRepoBaseInfoDTO) {
            return ((KbRepoBaseInfoDTO) dto).getCreateUserId();
        }
        return 0L;
    }

    private void fillTagsInfo(List<KbRepoBaseInfoDTO> kbRepoBaseInfoDTOS) {
        if (CollUtil.isEmpty(kbRepoBaseInfoDTOS)) {
            return;
        }
        KbRepoBaseInfoDTO peekRepo = kbRepoBaseInfoDTOS.getFirst();
        tagsQueryService.fillTagsInfo(
            kbRepoBaseInfoDTOS,
            TagTypeEnum.KNOWLEDGE,
            peekRepo.getTeamId(),
            KbRepoBaseInfoDTO::getId,
            KbRepoBaseInfoDTO::setTagList
        );
    }

    /**
     * 填充协作者信息
     */
    private void fillCollaborators(List<KbRepoBaseInfoDTO> kbRepoBaseInfoDTOS) {
        teamMemberQueryService.fillCollaborators(
            kbRepoBaseInfoDTOS,
            KbRepoBaseInfoDTO::getId,
            KbRepoBaseInfoDTO::getTeamId,
            KbRepoBaseInfoDTO::getCreateUserId,
            ResourceType.KB_REPO,
            KbRepoBaseInfoDTO::setCollaborators
        );
    }

    public List<CollaboratorDTO> listCollaborator(@Valid IdQuery query) {
        KbRepoBaseInfoPO kbRepoBaseInfoPO = mapper.selectById(query.getId());
        return teamMemberQueryService.getCollaborators(
            kbRepoBaseInfoPO.getId(),
            kbRepoBaseInfoPO.getTeamId(),
            kbRepoBaseInfoPO.getCreateUserId(),
            ResourceType.KB_REPO
        );
    }

    @IgnoreTenant
    public KbRepoBaseInfoDTO getById(Long repoId) {
       return assembler.PO2DTO(mapper.selectById(repoId));
    }
}

