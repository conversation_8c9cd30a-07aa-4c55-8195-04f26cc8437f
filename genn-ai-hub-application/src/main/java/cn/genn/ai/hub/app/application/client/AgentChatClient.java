package cn.genn.ai.hub.app.application.client;

import cn.genn.ai.cerebro.model.CerebroAuth;
import cn.genn.ai.hub.app.application.command.govow.ExecutionRequest;
import cn.genn.ai.hub.app.application.dto.govow.AgentMessage;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.spring.boot.starter.upm.model.SsoUserAuthInfoDTO;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.time.Duration;

@Slf4j
@Component
@RequiredArgsConstructor
public class AgentChatClient {

    private final WebClient webClient;
    private final GennAIHubProperties gennAIHubProperties;
    private static final String CHAT_COMPLETIONS_PATH = "/chat/completions";
    public static final String MAGIC_TOKEN = "magic-token";
    public static final String TOKEN = "token";

    public Flux<ServerSentEvent<AgentMessage>> chat(ExecutionRequest request, CerebroAuth auth) {
        String uri = gennAIHubProperties.getJob().getChatBaseUrl() + CHAT_COMPLETIONS_PATH;
        log.info("chat uri:{}",uri);
        WebClient.RequestBodySpec requestSpec = webClient
                .post()
                .uri(uri)
                .contentType(MediaType.APPLICATION_JSON);
        requestSpec = applyAuthentication(requestSpec, auth);

        return requestSpec
                .bodyValue(JsonUtils.toJsonNotNull(request))
                .retrieve()
                .bodyToFlux(new ParameterizedTypeReference<ServerSentEvent<AgentMessage>>() {
                })
                .timeout(Duration.ofSeconds(300))
                .doOnSubscribe(subscription -> {
                    log.info("开始调用Agent聊天接口，chatId: {}", request.getChatId());
                })
                .doOnError(error -> {
                    log.error("Agent聊天接口调用出错，chatId: {}", request.getChatId(), error);
                })
                .doOnComplete(() -> {
                    log.info("Agent聊天接口调用完成，chatId: {}", request.getChatId());
                });
    }

    /**
     * 应用认证信息到请求
     */
    private WebClient.RequestBodySpec applyAuthentication(WebClient.RequestBodySpec requestBodySpec, CerebroAuth auth) {
        switch (auth.getType()) {
            case API_KEY:
                return requestBodySpec.header(HttpHeaders.AUTHORIZATION, "Bearer " + auth.getApiKey());
            case COOKIE:
                requestBodySpec.header(HttpHeaders.COOKIE, auth.getCookie());
                if (CharSequenceUtil.isNotEmpty(auth.getToken())) {
                    requestBodySpec.header(TOKEN, auth.getToken());
                }
                return requestBodySpec;
            case MAGIC_TOKEN:
                requestBodySpec.header(MAGIC_TOKEN, auth.getMagicTokenData());
                return requestBodySpec;
            default:
                throw new IllegalArgumentException("Unsupported auth type: " + auth.getType());
        }
    }
}
