package cn.genn.ai.hub.app.application.api;

import cn.genn.ai.hub.app.infrastructure.repository.persistence.AgentInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.utils.vector.milvus.MemoryEventsVectorUtil;
import cn.genn.ai.hub.core.api.memory.MemoryEventsSearchAbilityApi;
import cn.genn.ai.hub.core.api.memory.MemoryEventsSearchRequest;
import cn.genn.ai.hub.core.api.memory.MemoryEventsSearchResponse;
import io.milvus.v2.service.vector.response.SearchResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 情景记忆事件检索能力接口实现
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class MemoryEventsSearchAbilityApiImpl implements MemoryEventsSearchAbilityApi {

    private final MemoryEventsVectorUtil memoryEventsVectorUtil;

    private final AgentInfoRepositoryImpl agentInfoRepository;

    @Override
    public MemoryEventsSearchResponse search(MemoryEventsSearchRequest request) {
        try {
            // 设置租户ID
            Long tenantId = agentInfoRepository.getTenantIdByWorkflowId(request.getWorkflowId());

            // 调用向量搜索
            SearchResp searchResp = memoryEventsVectorUtil.searchMemoryEvents(
                tenantId,
                request.getQuery(),
                request.getVectorModel(),
                request.getTopK()
            );

            List<MemoryEventsSearchResponse.MemoryEventSearchResultItem> items = new ArrayList<>();

            if (searchResp != null && searchResp.getSearchResults() != null && !searchResp.getSearchResults().isEmpty()) {
                List<List<SearchResp.SearchResult>> searchResults = searchResp.getSearchResults();
                if (!searchResults.isEmpty()) {
                    for (SearchResp.SearchResult result : searchResults.get(0)) {
                        MemoryEventsSearchResponse.MemoryEventSearchResultItem item =
                            MemoryEventsSearchResponse.MemoryEventSearchResultItem.builder()
                                .id(((Number) result.getEntity().get("memory_events_id")).longValue())
                                .score((double) result.getScore())
                                .subject((String) result.getEntity().get("subject"))
                                .time((String) result.getEntity().get("time"))
                                .event((String) result.getEntity().get("event"))
                                .cause((String) result.getEntity().get("cause"))
                                .process((String) result.getEntity().get("process"))
                                .build();

                        items.add(item);
                    }
                }
            }

            return MemoryEventsSearchResponse.builder()
                .items(items)
                .resultCount(items.size())
                .originalQuery(request.getQuery())
                .build();

        } catch (Exception e) {
            log.error("Error in memory events search", e);
            throw new RuntimeException("Failed to search memory events", e);
        }
    }
}
