package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.JobSaveOrUpdateCommand;
import cn.genn.ai.hub.app.application.command.govow.JobCompletionsCommand;
import cn.genn.ai.hub.app.application.dto.govow.ContentSupplement;
import cn.genn.ai.hub.app.application.dto.govow.JobTasksDTO;
import cn.genn.ai.hub.app.application.enums.ChatMode;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.job.xxl.model.XxlJobScheduleTypeEnum;
import cn.genn.job.xxl.model.task.*;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.util.ObjUtil;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.BeanUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GovowJobAssembler {

    default TaskSaveOrUpdateCommand jobCommand2TaskCommand(JobSaveOrUpdateCommand command, String bizKey) {
        TaskSaveOrUpdateCommand taskCommand = new TaskSaveOrUpdateCommand();
        if (ObjUtil.isNotNull(command.getId())) {
            taskCommand.setId(command.getId());
        }
        JobTaskPlan plan = new JobTaskPlan();
        plan.setType(XxlJobScheduleTypeEnum.CRON);
        plan.setCron(command.getCron());
        JobTaskContext context = new JobTaskContext();
        context.setTaskType(JobTaskTypeEnum.LOCAL);
        context.setReqTimeout(3600L);
        context.setClassName("cn.genn.ai.hub.app.application.service.govow.GovowJobService");
        context.setMethodName("completions");
        // 构建JobCompletionsCommand对象作为参数
        JobCompletionsCommand completionsCommand = new JobCompletionsCommand();
        completionsCommand.setUserId(CurrentUserHolder.getUserId());
        completionsCommand.setChatMode(command.getChatMode());
        completionsCommand.setContent(command.getContent());
        completionsCommand.setTaskName(command.getName());
        completionsCommand.setSupplements(command.getSupplements());

        Map<String, Object> params = new HashMap<>();
        params.put("command", completionsCommand);
        context.setParams(params);
        taskCommand.setName(command.getName());
        taskCommand.setBizKey(bizKey);
        taskCommand.setPlan(plan);
        taskCommand.setContent(context);
        taskCommand.setPriority(0);
        taskCommand.setStatus(Optional.ofNullable(command.getStatus()).orElse(TaskStatusEnum.RUNNING));
        return taskCommand;
    }

    List<JobTasksDTO> task2Job(List<JobScheduledTasksDTO> dtos);

    default JobTasksDTO task2Job(JobScheduledTasksDTO dto) {
        JobTasksDTO result = new JobTasksDTO();
        BeanUtils.copyProperties(dto, result);
        result.setCron(dto.getPlan().getCron());
        Map<String, Object> params = dto.getContent().getParams();
        Object commandObj = params.get("command");
        JobCompletionsCommand paramCommand = JsonUtils.parse(JsonUtils.toJson(commandObj), JobCompletionsCommand.class);
        String chatMode = paramCommand.getChatMode().getCode();
        String content = paramCommand.getContent();
        List<ContentSupplement> supplement = paramCommand.getSupplements();

        result.setChatMode(ChatMode.fromCode(chatMode));
        result.setContent(content);
        result.setSupplements(supplement);
        return result;
    }

}
