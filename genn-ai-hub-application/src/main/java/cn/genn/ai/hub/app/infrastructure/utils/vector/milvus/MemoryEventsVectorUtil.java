package cn.genn.ai.hub.app.infrastructure.utils.vector.milvus;

import cn.genn.ai.hub.app.application.dto.MemoryEventsDTO;
import cn.genn.ai.hub.app.application.dto.embedding.MemoryEventsEmbeddingDataDTO;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.spring.boot.starter.ai.component.AIModelManager;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.common.DataType;
import io.milvus.v2.service.vector.request.DeleteReq;
import io.milvus.v2.service.vector.request.InsertReq;
import io.milvus.v2.service.vector.request.SearchReq;
import io.milvus.v2.service.vector.request.data.FloatVec;
import io.milvus.v2.service.vector.response.SearchResp;
import jakarta.annotation.Resource;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * 情景记忆事件向量库操作
 */
@Component
@Slf4j
public class MemoryEventsVectorUtil {

    @Resource
    private MilvusClientV2 milvusClient;
    @Resource
    private AIModelManager aiModelManager;
    @Resource
    private GennAIHubProperties properties;

    public void createOrUpdateMemoryEventsVector(String embeddingModelKey, MemoryEventsDTO memoryEventsDTO) {
        try {
            log.info("Start creating or updating memory events vector, memoryEventsId: {}, embeddingModelKey: {}",
                memoryEventsDTO != null ? memoryEventsDTO.getId() : "null", embeddingModelKey);

            if (Objects.isNull(memoryEventsDTO)) {
                log.warn("MemoryEventsDTO is null, skipping vector creation/update");
                return;
            }

            // 删除已存在的向量数据
            DeleteReq deleteReq = DeleteReq.builder()
                .collectionName(properties.getMilvus().getCollectionNameOfMemoryEvents(embeddingModelKey))
                .filter("memory_events_id==" + memoryEventsDTO.getId())
                .build();
            log.info("Deleting existing vector data for memoryEventsId: {}", memoryEventsDTO.getId());
            milvusClient.delete(deleteReq);

            EmbeddingModel embeddingModel = aiModelManager.getEmbeddingModel(embeddingModelKey);
            // 拼接事件描述作为向量化内容：主体 + 事件
            String content = String.join(" ",
                memoryEventsDTO.getSubject(),
                memoryEventsDTO.getTime(),
                memoryEventsDTO.getEvent());
            log.info("Embedding content: {}", content);

            float[] embed = embeddingModel.embed(content);
            List<Float> embedding = new ArrayList<>(embed.length);
            for (Float f : embed) {
                embedding.add(f);
            }
            MemoryEventsEmbeddingDataDTO data = MemoryEventsEmbeddingDataDTO.builder()
                .tenantId(memoryEventsDTO.getTenantId())
                .memoryEventsId(memoryEventsDTO.getId())
                .subject(memoryEventsDTO.getSubject())
                .time(memoryEventsDTO.getTime())
                .event(memoryEventsDTO.getEvent())
                .cause(memoryEventsDTO.getCause())
                .process(memoryEventsDTO.getProcess())
                .embedding(embedding)
                .build();
            Gson gson = new Gson();
            JsonObject jsonObject = gson.toJsonTree(data).getAsJsonObject();

            milvusClient.insert(InsertReq.builder()
                .collectionName(properties.getMilvus().getCollectionNameOfMemoryEvents(embeddingModelKey))
                .data(Collections.singletonList(jsonObject))
                .build());

            log.info("Successfully created or updated memory events vector, memoryEventsId: {}", memoryEventsDTO.getId());
        } catch (Exception e) {
            log.error("Error creating or updating memory events vector, memoryEventsId: {}, embeddingModelKey: {}, error: {}",
                memoryEventsDTO != null ? memoryEventsDTO.getId() : "null", embeddingModelKey, e.getMessage(), e);
            throw e; // 重新抛出异常以便调用方可以处理
        }
    }

    public void deleteMemoryEventsVectorByMemoryEventsId(String vectorModelKey, Long memoryEventsId) {
        DeleteReq deleteReq = DeleteReq.builder()
            .collectionName(properties.getMilvus().getCollectionNameOfMemoryEvents(vectorModelKey))
            .filter("memory_events_id==" + memoryEventsId)
            .build();
        milvusClient.delete(deleteReq);
    }

    public void deleteMemoryEventsVectorByMemoryEventsIds(String vectorModelKey, List<Long> memoryEventsIds) {
        DeleteReq deleteReq = DeleteReq.builder()
            .collectionName(properties.getMilvus().getCollectionNameOfMemoryEvents(vectorModelKey))
            // 转换为类似 ['key1', 'key2', 'key3'] 的格式
            .filter("memory_events_id in " + memoryEventsIds
                .stream()
                .map(key -> Long.toString(key))
                .collect(Collectors.joining(", ", "[", "]")))
            .build();
        milvusClient.delete(deleteReq);
    }

    /**
     * 搜索情景记忆事件
     */
    public SearchResp searchMemoryEvents(Long tenantId, String query, String vectorModelKey, Integer topK) {
        try {
            // 获取嵌入模型
            EmbeddingModel embeddingModel = aiModelManager.getEmbeddingModel(vectorModelKey);

            // 对查询文本进行向量化
            float[] queryEmbed = embeddingModel.embed(query);
            FloatVec queryVector = new FloatVec(queryEmbed);

            // 构建搜索请求
            SearchReq searchReq = SearchReq.builder()
                .collectionName(properties.getMilvus().getCollectionNameOfMemoryEvents(vectorModelKey))
                .data(Collections.singletonList(queryVector))
                .annsField("embedding")
                .topK(topK)
                .filter("tenant_id == " + tenantId) // 租户隔离
                .outputFields(Arrays.asList("memory_events_id", "subject", "time", "event", "cause", "process"))
                .build();

            // 执行搜索
            return milvusClient.search(searchReq);

        } catch (Exception e) {
            log.error("Error searching memory events", e);
            return null;
        }
    }

}
